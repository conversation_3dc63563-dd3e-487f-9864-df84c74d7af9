# This is a generated file; do not edit or check into version control.
app_settings=/Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-6.1.1/
app_tracking_transparency=/Users/<USER>/.pub-cache/hosted/pub.dev/app_tracking_transparency-2.0.6+1/
appinio_social_share=/Users/<USER>/.pub-cache/hosted/pub.dev/appinio_social_share-0.3.2/
appsflyer_sdk=/Users/<USER>/.pub-cache/hosted/pub.dev/appsflyer_sdk-6.16.21/
audio_service=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service-0.18.18/
audio_service_web=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service_web-0.1.4/
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.2.2/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/
facebook_app_events=/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_app_events-0.19.7/
facebook_audience_network=/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_audience_network-1.0.1/
facebook_auth_desktop=/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-2.1.1/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.2/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/
firebase_crashlytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/
flutter_app_group_directory=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_app_group_directory-1.1.0/
flutter_facebook_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-7.1.2/
flutter_facebook_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-6.1.2/
flutter_inappwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/
flutter_inappwebview_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/
flutter_inappwebview_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/
flutter_inappwebview_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/
flutter_inappwebview_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/
flutter_inappwebview_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.3.0/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/
flutter_local_notifications_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
flutter_timezone=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_timezone-4.1.1/
freshchat_sdk=/Users/<USER>/.pub-cache/hosted/pub.dev/freshchat_sdk-0.10.26/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
in_app_purchase=/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase-3.2.2/
in_app_purchase_android=/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_android-0.4.0+2/
in_app_purchase_storekit=/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.22+1/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.10.4/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/
lecle_social_share=/Users/<USER>/.pub-cache/hosted/pub.dev/lecle_social_share-0.5.3/
live_activities=/Users/<USER>/.pub-cache/hosted/pub.dev/live_activities-2.4.1/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
photo_manager=/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/
realm=/Users/<USER>/.pub-cache/hosted/pub.dev/realm-20.1.1/
screen_brightness=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness-2.1.5/
screen_brightness_android=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-2.1.2/
screen_brightness_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-2.1.2/
screen_brightness_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-2.1.1/
screen_brightness_ohos=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ohos-2.1.0/
screen_brightness_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-2.1.0/
screen_protector=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sign_in_with_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/
sign_in_with_apple_web=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-3.0.0/
spine_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/spine_flutter-4.2.36/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
vpn_check=/Users/<USER>/.pub-cache/hosted/pub.dev/vpn_check-0.3.0/
